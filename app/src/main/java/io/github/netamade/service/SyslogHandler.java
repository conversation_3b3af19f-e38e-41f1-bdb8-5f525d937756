package io.github.netamade.service;

import android.accessibilityservice.AccessibilityService;
import android.content.Intent;
import android.util.Log;

import io.github.netamade.syslog.LogData;
import io.github.netamade.util.HozonSettings;

public class SyslogHandler {
    private final AccessibilityService context;

    public SyslogHandler(AccessibilityService context) {
        this.context = context;
    }

    public void handle(LogData logData) {
        SyslogEvent event = parseLogData(logData);
        if (event == null) {
            return;
        }
        Log.i("SyslogHandler", "捕获到日志事件: " + event.getName() + " => " + logData.getRawMessage());
        Intent intent = new Intent(HozonSettings.ACTION_SYSLOG_EVENT);
        intent.putExtra("event", event.name());
        context.sendBroadcast(intent);
    }

    private SyslogEvent parseLogData(LogData logData) {
        if (logData == null) {
            return null;
        }
        for (SyslogEvent event : SyslogEvent.values()) {
            if (event.getTag().equals(logData.getTag()) && logData.getMessage().contains(event.getMsg())) {
                return event;
            }
        }
        return null;
    }
}
