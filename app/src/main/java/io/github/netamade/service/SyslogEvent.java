package io.github.netamade.service;

import lombok.Getter;

@Getter
public enum SyslogEvent {

    // 车辆基本操作
    LockCar("锁车", "dms_service_app", "power_mode 0"),

    // 车门操作 - 哪吒S
    DoorRow1LeftOpen("开主驾门", "VehicleControllerlmpl", "onChangeEvent DOOR_ROW_1_LEFT:1"),
    DoorRow1LeftClose("关主驾门", "VehicleControllerlmpl", "onChangeEvent DOOR_ROW_1_LEFT:0"),
    DoorRow1RightOpen("开副驾门", "VehicleControllerlmpl", "onChangeEvent DOOR_ROW_1_RIGHT:1"),
    DoorRow1RightClose("关副驾门", "VehicleControllerlmpl", "onChangeEvent DOOR_ROW_1_RIGHT:0"),

    // 挂档操作
    GearPark("挂档-P档", "EnergyBackGroundData", "HZ_PWRSYS_VCU_READY_LIGHT_STATUS 0"),
    GearDrive("挂档-D档", "EnergyBackGroundData", "HZ_PWRSYS_VCU_READY_LIGHT_STATUS 1"),

    // 驾驶位操作
    DriverEnter("驾驶位进入", "dms_service_app", "checkHasDriver"),
    DriverLeave("驾驶位离开", "BiologicalOms", "hasDriver state 0"),

    // 座位检测 - 副驾驶位
    SeatCopilotOccupied("座位检测-副驾有人", "dms_service_app", "hasDriver: 0 areald 4 once true"),
    SeatCopilotEmpty("座位检测-副驾无人", "dms_service_app", "hasDriver: 1 areald 4 once true"),

    // 座位检测 - 后排左侧
    SeatRearLeftOccupied("座位检测-后排左侧有人", "dms_service_app", "hasDriver: 0 areald 16 once true"),
    SeatRearLeftEmpty("座位检测-后排左侧无人", "dms_service_app", "hasDriver: 1 areald 16 once true"),

    // 座位检测 - 后排中间
    SeatRearCenterOccupied("座位检测-后排中间有人", "dms_service_app", "hasDriver: 0 areald 32 once true"),
    SeatRearCenterEmpty("座位检测-后排中间无人", "dms_service_app", "hasDriver: 1 areald 32 once true"),

    // 座位检测 - 后排右侧
    SeatRearRightOccupied("座位检测-后排右侧有人", "dms_service_app", "hasDriver: 0 areald 64 once true"),
    SeatRearRightEmpty("座位检测-后排右侧无人", "dms_service_app", "hasDriver: 1 areald 64 once true"),

    // 系统初始化
    WifiInitializing("车辆座舱自检-WiFi初始化", "WifiHAL", "Initializing wifi"),

    // 后备箱操作
    TrunkOpen("开后备箱", "MultiWindowManager", "HZ_REAR_DOOR_ON: 1"),
    TrunkClose("关后备箱", "MultiWindowManager", "HZ_REAR_DOOR_STATUS: 0"),
    ;
    private final String name;
    private final String tag;
    private final String msg;

    SyslogEvent(String name, String tag, String msg) {
        this.name = name;
        this.tag = tag;
        this.msg = msg;
    }
}
